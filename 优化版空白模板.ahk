#SingleInstance Force
SetWorkingDir %A_ScriptDir%

; =================== 配置和初始化 ===================
global options := {}
global guiVars := {}

; 初始化选项数据
InitOptions() {
    options.position := "平卧位|左侧卧位|右侧卧位|平卧位,右侧背部垫高20度，"
    options.incision := "胸骨正中切口|右侧第四肋间前外侧小切口8cm|右侧腹股沟切口|右侧第2肋间前外侧小切口6cm|右侧第四肋间外侧小切口6cm|右侧锁骨下切口"
    options.pericardium := "心包切开并悬吊，|心包未切开，|胸膜完整，|左侧胸膜打开，|右侧胸膜打开，|经第4肋间进胸，右隔神经前2cm切开心包悬吊。"
    options.heart_surface := "心包无粘连，胸膜完整，|主动脉：肺动脉=1：0.75|主动脉：肺动脉=1：1，|主动脉：肺动脉=1：1.5，|左心室大，右室触及收缩期震颤。|左心室肥厚、扩大。|心房正位，心室右袢，主动脉位于右前，肺动脉位于左后，右室肥大，|心包内少量积液，心脏表面未见明显损伤，起搏导线位于心脏表面，胸腔内可见较多坏死组织和纤维素样物质，未见明显出血点。|胸骨正中切口，皮肤裂开，皮下组织红润，表面有白色分泌物。胸骨松动，有死骨。坏死组织多。"
    options.heparin := "未给肝素，|肝素化|"
    options.heparin_dose := "1mg/kg|3mg/kg|"
    options.artery_cannula := "升主动脉，|主动脉弓部，|右股动脉，|左股动脉，|右腋动脉，"
    options.vein_cannula := "上腔静脉插管，|下腔静脉插管，|右股静脉插管，|右房插管，"
    options.cardioplegia := "心脏不停跳，应用心脏表面固定器|升主动脉阻断后，顺行灌注心麻液，|升主动脉阻断后，切开升主动脉，直视下经左右冠脉开口顺行灌注心麻液，|升主动脉不阻断，降温，诱导室颤，"
    options.circulation := "降温至鼻咽温度25度，阻断无名动脉、左颈总动脉、左锁骨下动脉近端后，深低温停循环，经右腋动脉5-10ml/kg低流量选择性脑灌注，|降温至鼻咽温度25度，阻断无名动脉、左颈总动脉、左锁骨下动脉近端后，深低温停循环，经无名动脉5-10ml/kg低流量选择性脑灌注，|经无名动脉插管，左颈总动脉插管行双侧脑灌注，|降温至鼻咽温度18度 肛温20度,过程中28度心脏室颤,阻升主动脉顺灌心麻液心脏停跳满意，经右上肺静脉左心吸引，探查如上所述.温度到达后,停循环,"
    options.lv_drain := "未放置左心引流，|经卵圆孔处放置左心引流，|经右上肺静脉处放置左心引流，|经肺动脉干放置左心引流，|经左心耳放置左心引流，|心脏操作结束，头低位弓部排气，左心排气，主动脉部分松阻断，经心麻液针头排气，心脏自动复跳，窦性心律，复温"
    
    ; 右房操作选项
    options.ra_open := "套线阻断上下腔静脉，由右心耳至下腔静脉方向切开右房，|"
    options.ra_explore := "右心大ASD 2个，1为卵圆孔型，15*15mm，|近下腔静脉处有1房缺，直径7*10mm，|无肺静脉畸形引流，右室流出道无狭窄，|卵圆孔未闭，|ASD下腔型，大小分别为25*30mm，|三尖瓣关闭满意，|三尖瓣膈瓣，后瓣下移3-5cm，隔瓣发育差、下移2cm，|三尖瓣环扩张。|原发孔ASD大小16*16mm，无肺静脉畸形引流，右室流出道无狭窄；二尖瓣前瓣裂，约2mm宽。|四个肺静脉畸形引流入共干，向上方走行，汇入无名静脉-上腔静脉-右房。|右冠窦呈囊袋样扩张破入右房，囊袋大小约15*25mm,基底部宽约8mm，夹闭囊袋后，顺灌心麻液，心脏停跳满意。"
    options.ra_operation := "直接缝合卵圆孔，|心包补片，连续缝合房间隔缺损，|按改良迷宫路线消融右房路径，|三尖瓣Devage成形，自体心包条水平褥式缝合环缩三尖瓣前，隔瓣环，瓣环可容3指尖，|间断缝合植入三尖瓣成型环，|4-0编结线带垫片缝合折叠房化右室，修整三尖瓣环至31号测瓣器。沿三尖瓣前瓣根部切开瓣叶，然后心包补片5-0滑线连续缝合，补片加宽三尖瓣前瓣，增加前瓣面积。|4-0编结线缝合二尖瓣裂，左室注水试验二尖瓣关闭满意，取戊二醛固定的心包片修补ASD，5-0 Prolene连续缝合，末针排气打结，膨肺未见残余分流。右室流出道未探及狭窄，|切开右心房，扩大房缺，左房后壁切开，切开肺静脉共干，5—0滑线连续缝合肺静脉共干于左房后壁，自体心包片修补ASD，4—0 Prolene连续缝合|切除多余囊袋，涤纶补片4-0Prolene连续缝合，膨肺排气打结，外围囊袋连续缝合，覆盖补片，"
    options.ra_close := "5-0滑线连续缝合右房切口。|"
    
    ; 右室肺动脉操作选项
    options.dakaiyoushi := "经三尖瓣显露，|右心室流出道纵行切开2-3cm，|肺动脉干切开，经肺动脉瓣显露，"
    options.youshifeidongmaitancha := "室间隔膜部缺损8mm，|右室流出道无狭窄，三尖瓣关闭满意，|右室流出道，肺动脉瓣无狭窄，|VSD干下型缺损，大小5mm，与肺动脉瓣及主动脉瓣均无距离，|主动脉：肺动脉=1：1，肺动脉瓣交界处粘联狭窄，右室流出道、主肺动脉、左右肺动脉均无狭窄，三尖瓣关闭可。|VSD15*12mm，主动脉骑跨70，右室流出道狭窄，可过6mm探子，左、右肺动脉可通过8mm探子。|切开右肺动脉,主肺动脉,肺动脉主干内可见慢性附壁血栓.左肺动脉完全闭塞,右肺动脉极重度狭窄.肺动脉瓣叶少量血栓"
    options.caozuoinyoushi := "5-0滑线连续缝合修补室缺。|5-0滑线连续缝合牛心包补片修补室缺。|心包片行肺动脉瓣成形术。|经肺动脉干，4-0滑线带垫片褥式缝合动脉导管开口。|经肺动脉涤纶补片修补VSD， 4—0 编结线带垫片间断褥式缝合。|切开肺动脉瓣交界处，可通过15号流出道探子。右室流出道可通过20号探子。|右室流出道切开，探查如上所述，肺动脉瓣交界粘连，松解粘连、疏通右室流出道，可过13mm探子。牛心包补片修补VSD，5—0 Prolene连续缝合，5-0 Prolene连续缝合心包补片扩大右室流出道，膨肺未见残余分流，|应用神经剥离子,沿慢性血栓与肺动脉内膜的间隙分离,清除右肺主干,右肺上,中,下叶肺动脉血栓.20min后,重新恢复全身灌注,15min,然后再次清除左肺动脉血栓,清除左肺动脉主干及上下叶肺动脉及其分支内的机化血栓。"
    options.guanbiyoushi := "5-0滑线连续缝合右室切口。|5-0滑线连续缝合肺动脉切口。|5-0滑线连续缝合，心包补片加宽右室流出道。|5-0滑线连续缝合，心包补片加宽肺动脉瓣环。"
    
    ; 左房操作选项
    options.dakaizuofang := "阻断上下腔静脉，由右心耳至下腔静脉方向切开右房，然后切开房间隔显露左房。|经房间沟直接切开左房。"
    options.zuofangtancha := "无肺静脉畸形引流。|二尖瓣P2后瓣腱索断裂、瓣叶脱垂，二尖瓣瓣环扩大，重度关闭不全。|左心耳内膜增厚，二尖瓣增厚、钙化，瓣下腱索挛缩，二尖瓣开口幅度0.8cm2。|心房纤颤。|左心房顶部血栓约4cm*5cm*1cm。"
    options.mitralvalve := "切除前瓣，保留后瓣，置换 二尖瓣。|切除前瓣，保留后瓣及部分前瓣腱索，间断褥式缝合置换二尖瓣。|切除前瓣及后瓣，间断褥式缝合置换二尖瓣。|切除原有人工瓣膜，间断褥式缝合置换二尖瓣。|楔形切除脱垂瓣叶，3-0编结线间断缝合，左室注水试验未见返流。|植入人工腱索4根，一端固定于前乳头肌，一端固定于前瓣边缘。|间断缝合植入二尖瓣成型环。|按MAZEIV改良迷宫路线消融左房路径。"
    options.zuoxiner := "切除后缝合。|应用双10号线结扎。|4-0滑线经左房内连续缝闭。|切割闭合器切除左心耳。|应用心耳夹夹闭。"
    options.guanbizuofang := "3-0滑线连续缝合房间隔切口。|3-0滑线连续缝合左房切口。"
    
    ; 主动脉操作选项
    options.dakaizhudongmai := "升主动脉斜行切口，|"
    options.zhudongmaitancha := "（主动脉瓣）增厚，交界粘连，重度狭窄。|（主动脉瓣）二瓣化，type I 型。右无交界融合成假脊。交界粘连钙化，重度狭窄并关闭不全。| （主动脉瓣）左冠瓣赘生物形成，穿孔，重度关闭不全。| （主动脉瓣）无冠瓣脱垂，重度关闭不全。|（主动脉瓣）瓣环扩大，瓣膜关闭不全。|升主动脉瘤直径5cm，窦部3.5cm，|（主动脉根部瘤）窦部明显扩张，直径6cm，壁薄。冠脉开口上抬明显；|（冠脉开口）右冠状动脉开口异常，位于左冠窦，临近左右交界上方；|（弓缩窄-成人）降主动脉可见一局限性狭窄，狭窄处管径约6mm，狭窄处下方降主动脉直径约12mm，主动脉弓处直径约7mm，|（弓缩窄-婴儿）降主动脉可见一局限性狭窄，狭窄处外径约3mm，长度约2cm。狭窄处下方降主动脉直径约8mm，主动脉弓处直径约7mm，|（动脉导管）左锁骨下动脉对应的降主动脉对侧，降主动脉和左肺动脉之间有异常通道，直径8mm，长约10mm。有震颤，术后消失。|（夹层）升主动脉增粗约6cm，升主动脉,主动脉弓，降主动脉夹层，主动脉弓靠近左锁骨下动脉开口处见夹层破口2cm。|（夹层）无名动脉、左锁骨下动脉、左颈总动脉无夹层，右冠开口夹层累及上半周。主动脉瓣呈三叶结构，瓣叶质量可。|主动脉瓣下10mm局部环形膜性狭窄，致局部内径约10mm，主动脉关闭满意。"
    options.zhudongmaibanshoushu := "切除主动脉瓣，间断褥式缝合置换主动脉瓣。垫片位于左室面|（升主动脉置换wheat）5—0 Prolene将人造血管远端与主动脉窦管交界行端--端吻合，然后5-0滑线吻合人工血管于主动脉（无名动脉前方）。|（桩包Bentall）离断升主动脉，5—0 Prolene分别缝合左、右冠脉开口。用4—0 Prolene将人造血管远端与主动脉行端端吻合。缝合主动脉根部主动脉壁包裹人工血管并与右心房连通内引流。|（裸Bentall）切除多余主动脉窦壁，人工血管根部与瓣环上主动脉壁3-0滑线连续缝合（内垫心包条），缝合一周封闭加固。左冠状动脉与右冠脉开口Button，垫心包条加固止血吻合于人工血管。心麻液经人工血管灌注检查，未见出血。|（动脉导管处理）向下牵拉肺叶，沿降主动脉切开纵隔胸膜，向前牵拉，显露导管。仔细游离导管的上、下、后缘，控制性降压至80/60mmHg, 查无喉返神经损伤，双10#，单10#分别结扎导管的主动脉侧，肺动脉侧，震颤消失，查无出血，缝合纵隔胸膜，止血，双10#线缝合固定肋间切口。未放置左胸腔引流管。膨肺后逐层关胸。|（弓缩窄端端吻合）降温至鼻咽温25度。然后上下腔静脉插管，阻断。升主动脉插入心麻液针头，阻断升主动脉，顺行灌注HTK心脏麻醉液。右房切开，晶体吸出，经房间隔缺损放置左房引流管。然后将主动脉插管送入无名动脉，阻断左颈总动脉，左锁骨下动脉，开始选择性脑灌注。然后游离动脉导管及降主动脉，充分游离降主动脉至正常血管1.5cm，结扎切断动脉导管。应用心耳钳钳夹降主动脉并向上提拉，切除缩窄部的降主动脉，应用7-0滑线，连续缝合，端端吻合降主动脉-主动脉弓处。排气，开放阻断钳，开放左颈总动脉，开放左锁骨下动脉，开放无名动脉，并将主动脉插管送入主动脉弓方向。开始复温。 |（夹层根部处理）保留主动脉瓣，应用自体外膜内翻，连续水平褥式加连续缝合加固窦管交界一周，连续缝合，加固窦管交界。探查主动脉瓣叶对合好。|（夹层象鼻子处理）然后降温至鼻咽温度25度，阻断左颈总动脉、左锁骨下动脉、无名动脉，全身深低温停循环，剪断左颈总动脉、左锁骨下动脉，经无名动脉低流量选择性脑灌注，松升主动脉阻断，降主动脉远端置入远端26mm*100mm支架，四分叉血管远端、支架血管近心端与降主动脉近端行端端吻合，5—0 prolene线连续缝合。吻合完成后随即开放降主动脉远端血运。恢复全身灌注流量。|（夹层象鼻+岛状处理1）在术中支架人工血管合适位置修剪缺口，4-0滑线连续缝合，吻合无名动脉和左颈总动脉与象鼻支架人工血管部分。然后四分叉血管远端-降主动脉近端人工血管行端端吻合，4—0 prolene线连续缝合。吻合完成后随即开放无名动脉左锁骨下动脉排气，开放降主动脉远端血运。恢复全身灌注流量。|（夹层象鼻+岛状处理2）然后分叉血管与左锁骨下动脉近心端行端—端吻合，5-0prolene线连续缝合。最后行四分叉血管近心端与升主动脉近端吻合，5-0prolene线连续缝合，吻合|（夹层象鼻+岛状处理2）然后离断缝闭左锁骨下动脉近心端，将左锁骨下动脉与左颈总动脉行端—侧吻合，5-0prolene线连续缝合。|（夹层弓部处理）然后依次分叉血管与左颈总动脉远端吻合，5-0prolene线连续缝合，随即开放行双侧脑灌注；四分叉血管与左锁骨下动脉近心端吻合，5-0prolene线连续缝合。|（夹层心脏复跳）行四分叉血管近心端与升主动脉窦管交界处近端吻合，5-0prolene线连续缝合，吻合完成后，左心排气，开放升主动脉，复跳顺利；四分叉血管与无名动脉近心端行端—端吻合，5-0 prolene线连续缝合。温度正常后，渐停CPB，拔除腔房管及左心引流管，鱼精蛋白中和肝素，拔除主动脉插管，止血，|切除主动脉瓣下膜性狭窄，探查可通过20#探子，主动脉瓣关闭满意。"
    options.guanbizhudongmai := "5-0滑线连续缝合主动脉切口。|5-0滑线连续缝合，吻合升主动脉人工血管与主动脉弓部人工血管。|5-0滑线连续缝合，吻合升主动脉人工血管与主动脉弓部血管。"
    
    ; 左室操作选项
    options.xinjian := "3-0滑线双重带垫片6角型荷包，穿刺针，导丝，导管交换，引导鞘管经心尖进入左室。|左室心尖切开2cm，|左室室壁瘤切开，"
    options.caozuoinzuoshi := "5-0滑线连续缝合修补室缺。|5-0滑线连续缝合牛心包补片修补室缺。|射线引导下植入介入主动脉瓣膜。|切除多余瘤壁。|清除左心室血栓。"
    options.guanbixinjian := "4-0滑线三明治法带心包条连续缝合心尖切口。|2-0滑线三明治法带毡片条连续缝合室壁瘤切口。|心尖荷包打结，无出血。"
    
    ; 冠脉搭桥选项
    options.guanmaitancha := "心脏左侧壁及前外侧壁色紫。|冠状动脉病变如术前造影，可见多发、弥漫性病变。|LAD中段管径1.5mm。|OM动脉内径1.5mm。|RCA近端闭塞。|PDA内径1mm。|LPA闭塞。|LAD近端闭塞，远端管腔1.5mm。"
    options.qiaoxueguan := "桥血管选用LIMA，|RA，|RIMA，|左SVG，|右SVG。"
    options.wenhekou := "LIMA-LAD，|RA-RCA，|RA-OM，|RIMA-RCA，|AO-SVG-DIAG-OM，|AO-SVG-DIAG-OM-LCX，|AO-SVG-DIAG-OM-LCX-LPA，|AO-SVG-DIAG-OM-LCX-PDA，|在分叉前切开右冠，行内膜剥离术，剥脱LPA及分叉处闭塞内膜约2.5cm。|远端用7-0滑线吻合，主动脉吻合口应用6-0滑线吻合。应用冠脉血管多普勒测量，桥均流量好，PI低。"
    
    ; 停机和关胸选项
    options.CPBtingjimiaosu := "（室颤）头低位弓部排气，左心排气，主动脉部分松阻断，经心麻液针头排气，心脏室颤，电击除颤成功。窦性心律，复温，渐停CPB顺利。ABP101/73mmHg，RAP4mmHg，LAP10mmHg，循环稳定，鱼精蛋白中和肝素（1.5：1），拔除动脉插管，止血。|（自动复跳）左心排气，主动脉部分松阻断经心麻液针头排气，心脏自动复跳，窦性心律， ABP101/73mmHg，RAP4mmHg，LAP10mmHg，循环稳定，鱼精蛋白中和肝素（1.5：1），拔除动脉插管，止血。|IABP：患者心功能较差，停机较为困难，左心辅助停机，多巴酚丁胺，去甲肾上腺素，肾上腺素等多种血管活性药物维持较为困难，请ICU石慧娟会诊，急诊术中行右侧股动脉穿刺，置入导丝至降主动脉，顺利置入球囊，接IABP顺利。|（肺出血ECMO）停机后出现支气管内渗血，观察循环及呼吸，氧和功能差，血压逐渐下降，调整血管活性药物及呼吸机参数效果不佳，与家属沟通后，应用V-A ECMO辅助，静脉经右侧股静脉穿刺置入，动脉应用升主动脉插管，ECMO运转后呼吸及循环好转，稳定。"
    options.yinliuguanxiong := "（正中切口）清点器械、敷料数目无误，缝合部分心包，置心包、胸骨后引流管各一根，逐层关胸。|（右胸切口）清点器械、敷料数目无误，右胸引流管一根，逐层关胸。|（正中+左胸引流）清点器械、敷料数目无误，心包、胸骨后引流管，左胸引流管一根，逐层关胸。|（左胸切口PDA）间断缝合纵膈胸膜，未放置左胸引流。逐层关胸。|（耗材）应用常州华森胸骨板*3，胸骨钉固定胸骨。|（探查止血）拆除胸部正中切口缝线，暴露心脏，探查如上所述，拆除引流管和起搏导线，彻底清除胸腔内坏死组织和纤维素样物质，然后分别以温盐水、双氧水、庆大霉素、温盐水冲洗数次，彻底止血，|（胸骨清创+肌瓣）切除原切口周围2mm皮缘及皮下组织，清除坏死的皮下组织，去除钢丝，及线头异物，咬骨钳清除死骨，双氧水，络合碘，生理盐水反复冲洗，游离双侧胸大肌肌瓣，腋前线附近离断，向胸骨中线翻转，填充。置胸骨正中、双侧胸大肌皮下间隙引流管各1根，接负压吸引。10号线间断水平褥式缝合固定肌瓣。 10号线减张缝合10针。清点器械，敷料无误，再次消毒皮肤，10号线间断缝合皮肤。再次消毒，盖敷料，|（纱布填塞）止血，清点器械、敷料数目无误，置心包及纵膈引流管各1根，因渗出较多，纵膈后放置一块纱垫，延迟关胸，逐层关胸。"
    
    ; 移植物选项
    options.sanjianbanyizhiwu := "机械瓣|生物瓣|成型环"
    options.shenchangongsisan := "--美敦力公司|--圣犹达公司|--爱德华公司|--佰仁思|"
    options.yizhiwudaxiaosan := "--26|--28|--30|"
    options.erjianbanyizhiwu := "机械瓣|生物瓣|成型环"
    options.shengchangongsier := "--美敦力公司|--圣犹达公司|--爱德华公司|--佰仁思|"
    options.yizhiwudaxiaoer := "--27|--28|--29|--30|--32|--34|--36"
    options.zhudongmaibanyizhiwu := "机械瓣|生物瓣|"
    options.abanmogongsi := "--美敦力公司|--圣犹达公司|--爱德华公司|--佰仁思"
    options.yizhiwudaxiaozhu := "--19|--20|--21|--23|--25|"
    options.rengongxueguan := "四分叉血管，|直血管，|"
    options.graftgongsi := "--马奎公司|--泰尔茂|--上海微创"
    options.sizegraft := "--26|--28|--30|"
    options.xiangbizhijia := "象鼻支架血管|"
    options.xiangbizhijiagongsi := "--上海微创|"
    options.sizexiangbizhijia := "--26*100|--28*100|"
}

; =================== GUI创建函数 ===================
CreateListBox(label, varName, options, x, y, w := 700, r := 4) {
    Gui, Add, Text, x%x% y%y%, %label%
    y += 20
    Gui, Add, ListBox, vvar_%varName% x%x% y%y% w%w% r%r%, %options%
    return y + (r * 20) + 10
}

CreateTab1() {
    y := 50
    y := CreateListBox("体位", "position", options.position, 50, y)
    y := CreateListBox("切口", "incision", options.incision, 50, y, 700, 6)
    y := CreateListBox("心包 切开?胸膜 切开", "pericardium", options.pericardium, 50, y, 700, 6)
    y := CreateListBox("心脏表面探查", "heart_surface", options.heart_surface, 50, y, 1450, 9)
}

CreateTab2() {
    y := 50
    y := CreateListBox("是否肝素", "heparin", options.heparin, 50, y)
    y := CreateListBox("肝素剂量", "heparin_dose", options.heparin_dose, 50, y)
    y := CreateListBox("动脉插管位置", "artery_cannula", options.artery_cannula, 50, y, 700, 5)
    y := CreateListBox("静脉插管位置", "vein_cannula", options.vein_cannula, 50, y, 700, 4)
    y := CreateListBox("心脏是否停跳，及停跳方式", "cardioplegia", options.cardioplegia, 50, y, 1200, 4)
    y := CreateListBox("深低温停循环和选择性脑灌注  右腋动脉/无名动脉", "circulation", options.circulation, 50, y, 1450, 4)
    y := CreateListBox("左心引流的放置", "lv_drain", options.lv_drain, 50, y, 700, 5)
}

CreateTab3() {
    y := 50
    y := CreateListBox("经由右房：", "ra_open", options.ra_open, 50, y, 700, 1)
    y := CreateListBox("右房探查", "ra_explore", options.ra_explore, 50, y, 1450, 12)
    y := CreateListBox("右房里的操作：", "ra_operation", options.ra_operation, 50, y, 1450, 6)
    y := CreateListBox("关闭右房：", "ra_close", options.ra_close, 50, y, 700, 1)
}

CreateTab4() {
    y := 50
    y := CreateListBox("经由右室 肺动脉：", "dakaiyoushi", options.dakaiyoushi, 50, y, 700, 3)
    y := CreateListBox("右室肺动脉探查", "youshifeidongmaitancha", options.youshifeidongmaitancha, 50, y, 1200, 9)
    y := CreateListBox("右室肺动脉里的操作：", "caozuoinyoushi", options.caozuoinyoushi, 50, y, 1450, 9)
    y := CreateListBox("关闭右室肺动脉：", "guanbiyoushi", options.guanbiyoushi, 50, y, 700, 4)
}

CreateTab5() {
    y := 50
    y := CreateListBox("经由左房：", "dakaizuofang", options.dakaizuofang, 50, y, 700, 2)
    y := CreateListBox("左房探查", "zuofangtancha", options.zuofangtancha, 50, y, 700, 5)
    y := CreateListBox("二尖瓣瓣膜及瓣环", "mitralvalve", options.mitralvalve, 50, y, 700, 8)
    y := CreateListBox("左心耳的处理", "zuoxiner", options.zuoxiner, 50, y, 400, 5)
    y := CreateListBox("关闭左房：", "guanbizuofang", options.guanbizuofang, 50, y, 400, 2)
    
    ; 二尖瓣移植物描述
    Gui, Add, Text, x1000 y300, 二尖瓣移植物描述
    Gui, Add, ListBox, vvar_erjianbanyizhiwu x1000 y320 w400 r3, %options.erjianbanyizhiwu%
    Gui, Add, Text, x1000 y380, 生产公司--二尖瓣
    Gui, Add, ListBox, vvar_shengchangongsier x1000 y400 w400 r4, %options.shengchangongsier%
    Gui, Add, Text, x1000 y480, 植入物大小 --二尖瓣
    Gui, Add, ListBox, vvar_yizhiwudaxiaoer x1000 y500 w400 r3, %options.yizhiwudaxiaoer%
}

CreateTab6() {
    y := 50
    y := CreateListBox("主动脉瓣，根部，血管手术：", "dakaizhudongmai", options.dakaizhudongmai, 50, y, 700, 2)
    y := CreateListBox("主动脉探查：", "zhudongmaitancha", options.zhudongmaitancha, 50, y, 1450, 12)
    y := CreateListBox("主动脉瓣，根部，血管手术", "zhudongmaibanshoushu", options.zhudongmaibanshoushu, 50, y, 1450, 11)
    y := CreateListBox("关闭主动脉：", "guanbizhudongmai", options.guanbizhudongmai, 50, y, 500, 3)
    
    ; 主动脉瓣移植物描述
    Gui, Add, Text, x580 y640 w150, 主动脉瓣移植物描述
    Gui, Add, ListBox, vvar_zhudongmaibanyizhiwu x580 y660 w150 r1, %options.zhudongmaibanyizhiwu%
    Gui, Add, Text, x580 y680 w150, 生产公司--主动脉瓣
    Gui, Add, ListBox, vvar_abanmogongsi x580 y700 w150 r1, %options.abanmogongsi%
    Gui, Add, Text, x580 y720 w150, 植入物大小 --主动脉瓣
    Gui, Add, ListBox, vvar_yizhiwudaxiaozhu x580 y740 w150 r1, %options.yizhiwudaxiaozhu%
    
    ; 人工血管描述
    Gui, Add, Text, x780 y640 w150, 人工血管描述
    Gui, Add, ListBox, vvar_rengongxueguan x780 y660 w150 r1, %options.rengongxueguan%
    Gui, Add, Text, x780 y680 w150, 生产公司--graft
    Gui, Add, ListBox, vvar_graftgongsi x780 y700 w150 r1, %options.graftgongsi%
    Gui, Add, Text, x780 y720 w150, 植入物大小 --graft
    Gui, Add, ListBox, vvar_sizegraft x780 y740 w150 r1, %options.sizegraft%
    
    ; 象鼻支架描述
    Gui, Add, Text, x980 y640 w150, 象鼻支架
    Gui, Add, ListBox, vvar_xiangbizhijia x980 y660 w150 r1, %options.xiangbizhijia%
    Gui, Add, Text, x980 y680 w150, 生产公司--象鼻支架
    Gui, Add, ListBox, vvar_xiangbizhijiagongsi x980 y700 w150 r1, %options.xiangbizhijiagongsi%
    Gui, Add, Text, x980 y720 w150, 植入物大小 --graft
    Gui, Add, ListBox, vvar_sizexiangbizhijia x980 y740 w150 r1, %options.sizexiangbizhijia%
}

CreateTab7() {
    y := 50
    y := CreateListBox("经由心尖：", "xinjian", options.xinjian, 50, y, 700, 3)
    y := CreateListBox("左心室里的操作：", "caozuoinzuoshi", options.caozuoinzuoshi, 50, y, 700, 5)
    y := CreateListBox("关闭心尖：", "guanbixinjian", options.guanbixinjian, 50, y, 700, 3)
}

CreateTab8() {
    y := 50
    y := CreateListBox("冠脉探查", "guanmaitancha", options.guanmaitancha, 50, y, 700, 9)
    y := CreateListBox("桥血管：", "qiaoxueguan", options.qiaoxueguan, 50, y, 700, 5)
    y := CreateListBox("吻合口描述，内膜剥脱：", "wenhekou", options.wenhekou, 50, y, 1200, 10)
}

CreateTab9() {
    y := 50
    y := CreateListBox("体外循环停机描述：", "CPBtingjimiaosu", options.CPBtingjimiaosu, 50, y, 1450, 8)
    y := CreateListBox("关胸，引流：", "yinliuguanxiong", options.yinliuguanxiong, 50, y, 1450, 8)
}

; =================== 主程序 ===================
InitOptions()

; 创建GUI
Gui, Font, S12 CDefault, Verdana
Gui, Add, Tab, x42 y10 w1500 h2000 vMainTab, 1 体位 麻醉方式|肝素 体外循环 左心引流|经右房操作|经右室肺动脉操作|经左房操作|主动脉瓣，根部，血管手术 |经左室操作|搭桥|体外循环停机关胸，引流描述

; 创建各个标签页
Gui, Tab, 1
CreateTab1()

Gui, Tab, 2
CreateTab2()

Gui, Tab, 3
CreateTab3()

Gui, Tab, 4
CreateTab4()

Gui, Tab, 5
CreateTab5()

Gui, Tab, 6
CreateTab6()

Gui, Tab, 7
CreateTab7()

Gui, Tab, 8
CreateTab8()

Gui, Tab, 9
CreateTab9()

Gui, Tab ; 表示之后创建的控件不属于属性页控件
Gui, Add, Text, x1200 y700 w300 h50, 完成后点击OK：
Gui, Add, Button, x1200 y750 w100 h30, OK

; 添加快捷键支持
Gui, Add, Text, x1200 y800 w300 h50, 快捷键：Ctrl+Enter 确认

Gui, Show, w1600 h900, 病程记录模板生成器
return

; =================== 事件处理 ===================
ButtonOK:
GuiClose:
GuiEscape:
Gui, Submit ; 保存每个与控件相关联的变量

; 等待用户确认
KeyWait, LButton, D
Sleep, 150

; 清空剪贴板
clipboard := ""

; 生成输出内容
output := GenerateOutput()

; 复制到剪贴板
clipboard := output
ClipWait, 2

; 等待用户点击后粘贴
KeyWait, LButton, D
Sleep, 150
SendInput, ^v

ExitApp

; =================== 输出生成函数 ===================
GenerateOutput() {
    output := ""
    output .= "基本情况:`n"
    output .= "体位=" . var_position . "`n"
    output .= "切口=" . var_incision . "`n"
    output .= "心包胸膜是否切开=" . var_pericardium . "`n"
    output .= "心脏表面探查=" . var_heart_surface . "`n`n"
    
    output .= "抗凝及体外循环:`n"
    output .= "是否肝素=" . var_heparin . "`n"
    output .= "肝素剂量=" . var_heparin_dose . "`n"
    output .= "动脉插管位置=" . var_artery_cannula . "`n"
    output .= "静脉插管位置=" . var_vein_cannula . "`n"
    output .= "心脏是否停跳及停跳方式=" . var_cardioplegia . "`n"
    output .= "左心引流的放置=" . var_lv_drain . "`n"
    output .= "深低温停循环和选择性脑灌注=" . var_circulation . "`n`n"
    
    output .= "右房内操作:`n"
    output .= "打开右房=" . var_ra_open . "`n"
    output .= "右房内探查=" . var_ra_explore . "`n"
    output .= "右房内的操作=" . var_ra_operation . "`n"
    output .= "关闭右房时=" . var_ra_close . "`n`n"
    
    output .= "右室内操作:`n"
    output .= "打开右室=" . var_dakaiyoushi . "`n"
    output .= "右室肺动脉探查=" . var_youshifeidongmaitancha . "`n"
    output .= "右室内操作=" . var_caozuoinyoushi . "`n"
    output .= "关闭右室肺动脉=" . var_guanbiyoushi . "`n`n"
    
    output .= "左房内操作:`n"
    output .= "打开左房=" . var_dakaizuofang . "`n"
    output .= "左房探查=" . var_zuofangtancha . "`n"
    output .= "二尖瓣瓣膜及瓣环=" . var_mitralvalve . "`n"
    output .= "左心耳的处理=" . var_zuoxiner . "`n"
    output .= "关闭左房=" . var_guanbizuofang . "`n`n"
    
    output .= "主动脉内操作:`n"
    output .= "打开主动脉=" . var_dakaizhudongmai . "`n"
    output .= "主动脉内探查=" . var_zhudongmaitancha . "`n"
    output .= "主动脉瓣，根部，血管手术=" . var_zhudongmaibanshoushu . "`n"
    output .= "关闭主动脉=" . var_guanbizhudongmai . "`n`n"
    
    output .= "经心尖操作:`n"
    output .= "经心间=" . var_xinjian . "`n"
    output .= "左室内的操作=" . var_caozuoinzuoshi . "`n"
    output .= "关闭心尖=" . var_guanbixinjian . "`n`n"
    
    output .= "冠脉搭桥:`n"
    output .= "冠脉探查=" . var_guanmaitancha . "`n"
    output .= "血管桥材料=" . var_qiaoxueguan . "`n"
    output .= "吻合口描述，内膜剥脱=" . var_wenhekou . "`n`n"
    
    output .= "体外循环停机描述:`n"
    output .= var_CPBtingjimiaosu . "`n"
    output .= "放置引流关胸=" . var_yinliuguanxiong . "`n`n"
    
    output .= "移植物描述:`n"
    output .= "三尖瓣植入物=" . var_sanjianbanyizhiwu . " 生产公司=" . var_shenchangongsisan . " 移植物型号=" . var_yizhiwudaxiaosan . "`n"
    output .= "二尖瓣植入物=" . var_erjianbanyizhiwu . " 生产公司=" . var_shengchangongsier . " 植入物型号=" . var_yizhiwudaxiaoer . "`n"
    output .= "主动脉瓣植入物=" . var_zhudongmaibanyizhiwu . " 主动脉瓣植入物-生产公司=" . var_abanmogongsi . " 主动脉瓣植入物型号=" . var_yizhiwudaxiaozhu . "`n"
    output .= "人工血管=" . var_rengongxueguan . " 人工血管生产公司=" . var_graftgongsi . " 人工血管型号=" . var_sizegraft . "`n"
    output .= "象鼻支架=" . var_xiangbizhijia . " 象鼻支架生产公司=" . var_xiangbizhijiagongsi . " 象鼻支架型号=" . var_sizexiangbizhijia . "`n"
    
    return output
}

; =================== 快捷键支持 ===================
^Enter::
    Gosub, ButtonOK
return 